'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase'
import { Payment } from '@/types'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { ExclamationTriangleIcon, PhoneIcon } from '@heroicons/react/24/outline'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function OverduePayments() {
  const [overduePayments, setOverduePayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadOverduePayments()
  }, [])

  const loadOverduePayments = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const today = new Date().toISOString().split('T')[0]

      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          lease:leases (
            property:properties (name),
            tenant:tenants (full_name, phone)
          )
        `)
        .eq('user_id', user.id)
        .in('status', ['pending', 'overdue'])
        .lt('due_date', today)
        .order('due_date', { ascending: true })
        .limit(5)

      if (error) throw error
      setOverduePayments(data || [])
    } catch (error) {
      console.error('خطأ في جلب المدفوعات المتأخرة:', error)
    } finally {
      setLoading(false)
    }
  }

  const getDaysOverdue = (dueDate: string) => {
    const today = new Date()
    const due = new Date(dueDate)
    const diffTime = today.getTime() - due.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">المدفوعات المتأخرة</h3>
        </div>
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">المدفوعات المتأخرة</h3>
        <Link
          href="/dashboard/payments?filter=overdue"
          className="text-sm text-danger-600 hover:text-danger-500"
        >
          عرض الكل
        </Link>
      </div>

      {overduePayments.length === 0 ? (
        <div className="text-center py-8">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-success-100 mb-4">
            <svg className="h-6 w-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <p className="text-gray-500">لا توجد مدفوعات متأخرة</p>
          <p className="text-sm text-gray-400 mt-1">جميع المدفوعات محدثة!</p>
        </div>
      ) : (
        <div className="space-y-3">
          {overduePayments.map((payment) => (
            <div
              key={payment.id}
              className="flex items-center justify-between p-3 bg-danger-50 border border-danger-200 rounded-lg"
            >
              <div className="flex items-center space-x-3 space-x-reverse">
                <ExclamationTriangleIcon className="h-5 w-5 text-danger-500 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {payment.lease?.tenant?.full_name || 'غير محدد'}
                  </p>
                  <p className="text-xs text-gray-600">
                    {payment.lease?.property?.name || 'غير محدد'}
                  </p>
                  <p className="text-xs text-danger-600">
                    متأخر {getDaysOverdue(payment.due_date)} يوم
                  </p>
                  <p className="text-xs text-gray-400">
                    تاريخ الاستحقاق: {format(new Date(payment.due_date), 'dd/MM/yyyy', { locale: ar })}
                  </p>
                </div>
              </div>
              <div className="text-left flex flex-col items-end space-y-2">
                <p className="text-sm font-medium text-gray-900">
                  {payment.amount.toLocaleString()} ر.س
                </p>
                {payment.lease?.tenant?.phone && (
                  <a
                    href={`tel:${payment.lease.tenant.phone}`}
                    className="inline-flex items-center px-2 py-1 bg-primary-100 text-primary-700 rounded-md text-xs hover:bg-primary-200 transition-colors"
                  >
                    <PhoneIcon className="h-3 w-3 ml-1" />
                    اتصال
                  </a>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
