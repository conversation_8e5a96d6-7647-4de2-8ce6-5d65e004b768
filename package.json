{"name": "rental-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "@supabase/supabase-js": "^2.38.5", "@supabase/auth-helpers-nextjs": "^0.8.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hook-form": "^7.48.2", "date-fns": "^2.30.0", "recharts": "^2.8.0", "clsx": "^2.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4"}}