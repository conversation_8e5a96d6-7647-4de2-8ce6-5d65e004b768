import {
  BuildingOfficeIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'
import { DashboardStats } from '@/types'

interface StatsCardsProps {
  stats: DashboardStats
}

export default function StatsCards({ stats }: StatsCardsProps) {
  const cards = [
    {
      name: 'إجمالي العقارات',
      value: stats.total_properties,
      icon: BuildingOfficeIcon,
      color: 'bg-primary-500',
      details: `${stats.rented_properties} مؤجر، ${stats.available_properties} متاح`
    },
    {
      name: 'المستأجرين',
      value: stats.total_tenants,
      icon: UsersIcon,
      color: 'bg-success-500',
      details: 'إجمالي المستأجرين النشطين'
    },
    {
      name: 'الدخل الشهري',
      value: `${stats.monthly_income.toLocaleString()} ر.س`,
      icon: CurrencyDollarIcon,
      color: 'bg-warning-500',
      details: `صافي الربح: ${(stats.monthly_income - stats.total_expenses).toLocaleString()} ر.س`
    },
    {
      name: 'المدفوعات المتأخرة',
      value: stats.overdue_payments,
      icon: ExclamationTriangleIcon,
      color: 'bg-danger-500',
      details: `${stats.pending_payments} معلق، ${stats.overdue_payments} متأخر`
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      {cards.map((card) => (
        <div
          key={card.name}
          className="relative overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:px-6 sm:py-6"
        >
          <dt>
            <div className={`absolute rounded-md p-3 ${card.color}`}>
              <card.icon className="h-6 w-6 text-white" aria-hidden="true" />
            </div>
            <p className="mr-16 truncate text-sm font-medium text-gray-500">
              {card.name}
            </p>
          </dt>
          <dd className="mr-16 flex items-baseline">
            <p className="text-2xl font-semibold text-gray-900">{card.value}</p>
          </dd>
          <div className="mt-2">
            <p className="text-xs text-gray-500">{card.details}</p>
          </div>
        </div>
      ))}
    </div>
  )
}
