'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { MonthlyReport } from '@/types'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function MonthlyChart() {
  const [data, setData] = useState<MonthlyReport[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadMonthlyData()
  }, [])

  const loadMonthlyData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const monthlyData: MonthlyReport[] = []
      const currentDate = new Date()
      
      // جلب بيانات آخر 6 أشهر
      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1)
        const year = date.getFullYear()
        const month = date.getMonth()
        
        const startDate = new Date(year, month, 1).toISOString().split('T')[0]
        const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0]

        // جلب المدفوعات للشهر
        const { data: payments } = await supabase
          .from('payments')
          .select('amount, status')
          .eq('user_id', user.id)
          .gte('payment_date', startDate)
          .lte('payment_date', endDate)
          .eq('status', 'paid')

        // جلب المصروفات للشهر
        const { data: expenses } = await supabase
          .from('expenses')
          .select('amount')
          .eq('user_id', user.id)
          .gte('expense_date', startDate)
          .lte('expense_date', endDate)

        const income = payments?.reduce((sum, p) => sum + p.amount, 0) || 0
        const expenseTotal = expenses?.reduce((sum, e) => sum + e.amount, 0) || 0
        const profit = income - expenseTotal

        monthlyData.push({
          month: date.toLocaleDateString('ar-SA', { month: 'short', year: 'numeric' }),
          income,
          expenses: expenseTotal,
          profit,
          payments_count: payments?.length || 0,
          overdue_count: 0, // سيتم حسابها لاحقاً إذا لزم الأمر
        })
      }

      setData(monthlyData)
    } catch (error) {
      console.error('خطأ في جلب البيانات الشهرية:', error)
    } finally {
      setLoading(false)
    }
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value.toLocaleString()} ر.س
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">التقرير الشهري</h3>
        </div>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">التقرير الشهري</h3>
        <div className="flex items-center space-x-4 space-x-reverse text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-primary-500 rounded-full ml-2"></div>
            <span>الدخل</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-danger-500 rounded-full ml-2"></div>
            <span>المصروفات</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-success-500 rounded-full ml-2"></div>
            <span>الربح</span>
          </div>
        </div>
      </div>

      {data.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">لا توجد بيانات للعرض</p>
        </div>
      ) : (
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="month" 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}ك`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="income" fill="#3b82f6" name="الدخل" radius={[2, 2, 0, 0]} />
              <Bar dataKey="expenses" fill="#ef4444" name="المصروفات" radius={[2, 2, 0, 0]} />
              <Bar dataKey="profit" fill="#22c55e" name="الربح" radius={[2, 2, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  )
}
