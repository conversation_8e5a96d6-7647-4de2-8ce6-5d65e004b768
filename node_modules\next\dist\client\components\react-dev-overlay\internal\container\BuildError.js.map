{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/BuildError.tsx"], "names": ["BuildError", "styles", "message", "versionInfo", "noop", "React", "useCallback", "Overlay", "fixed", "Dialog", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "className", "h4", "id", "VersionStalenessInfo", "DialogBody", "Terminal", "content", "footer", "p", "small", "css"], "mappings": ";;;;;;;;;;;;;;;IAeaA,UAAU;eAAVA;;IAmCAC,MAAM;eAANA;;;;;iEAlDU;wBAOhB;yBACiB;0BACC;sCACY;8BACT;;;;;;;;;;AAIrB,MAAMD,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEE,OAAO,EACPC,WAAW,EACZ,GAHwE;IAIvE,MAAMC,OAAOC,OAAMC,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,qBAACC,gBAAO;QAACC,OAAAA;qBACP,qBAACC,cAAM;QACLC,MAAK;QACLC,mBAAgB;QAChBC,oBAAiB;QACjBC,SAAST;qBAET,qBAACU,qBAAa,sBACZ,qBAACC,oBAAY;QAACC,WAAU;qBACtB,qBAACC;QAAGC,IAAG;OAAsC,sBAC5Cf,4BAAc,qBAACgB,0CAAoB,EAAKhB,eAAkB,qBAE7D,qBAACiB,kBAAU;QAACJ,WAAU;qBACpB,qBAACK,kBAAQ;QAACC,SAASpB;sBACnB,qBAACqB,8BACC,qBAACC;QAAEN,IAAG;qBACJ,qBAACO,eAAM;AAWvB;AAEO,MAAMxB,aAASyB,kBAAG"}