@echo off
title نظام إدارة الإيجارات
color 0A

echo.
echo ========================================
echo    نظام إدارة الإيجارات والمحاسبة
echo ========================================
echo.

cd /d "%~dp0"

echo [1/3] التحقق من Node.js...
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js من https://nodejs.org
    echo أو تشغيل: winget install OpenJS.NodeJS
    pause
    exit /b 1
) else (
    echo ✅ Node.js مثبت
)

echo.
echo [2/3] تثبيت التبعيات...
"C:\Program Files\nodejs\npm.cmd" install --silent
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت التبعيات
    echo جاري المحاولة مرة أخرى...
    "C:\Program Files\nodejs\npm.cmd" install --force --silent
)

echo ✅ تم تثبيت التبعيات

echo.
echo [3/3] تشغيل الخادم...
echo.
echo 🌐 افتح المتصفح واذهب إلى: http://localhost:3000
echo 🔄 لإيقاف الخادم اضغط Ctrl+C
echo.

"C:\Program Files\nodejs\npm.cmd" run dev

echo.
echo تم إيقاف الخادم
pause
