# نظام إدارة الإيجارات والمحاسبة المكتبية

نظام شامل لإدارة العقارات والإيجارات والمحاسبة المكتبية مبني بـ Next.js و TypeScript و Supabase.

## المميزات الرئيسية

### 🏢 إدارة العقارات
- إضافة وتعديل وحذف العقارات
- تصنيف العقارات (شقق، منازل، تجاري، مكاتب)
- تتبع حالة العقار (متاح، مؤجر، صيانة)
- إدارة تفاصيل العقار والوصف

### 👥 إدارة المستأجرين
- قاعدة بيانات شاملة للمستأجرين
- معلومات الاتصال وجهات الاتصال الطارئة
- تتبع تاريخ المستأجر

### 📋 إدارة عقود الإيجار
- إنشاء وإدارة عقود الإيجار
- ربط العقارات بالمستأجرين
- تتبع تواريخ بداية ونهاية العقود
- إدارة مبالغ الإيجار والودائع

### 💰 إدارة المدفوعات
- تتبع مدفوعات الإيجار
- إدارة المدفوعات المعلقة والمتأخرة
- طرق دفع متعددة (نقد، تحويل بنكي، شيك، بطاقة)
- تاريخ شامل للمدفوعات

### 📊 المصروفات والتقارير
- تتبع مصروفات العقارات
- تصنيف المصروفات (صيانة، مرافق، تأمين، ضرائب)
- تقارير مالية شاملة
- تحليل الأرباح والخسائر

### 🔔 الإشعارات والتنبيهات
- تنبيهات المدفوعات المستحقة
- إشعارات المدفوعات المتأخرة
- تذكيرات انتهاء العقود

## التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Database & Auth**: Supabase
- **Styling**: Tailwind CSS مع دعم RTL
- **UI Components**: Headless UI
- **Icons**: Heroicons
- **Charts**: Recharts
- **Forms**: React Hook Form
- **Date Handling**: date-fns

## متطلبات التشغيل

- Node.js 18+ 
- npm أو yarn
- حساب Supabase

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd rental-management
```

### 2. تثبيت التبعيات
```bash
npm install
# أو
yarn install
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env.local.example` إلى `.env.local` وأضف بيانات Supabase:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 4. إعداد قاعدة البيانات
قم بتشغيل SQL scripts في Supabase لإنشاء الجداول والسياسات المطلوبة.

### 5. تشغيل التطبيق
```bash
npm run dev
# أو
yarn dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## هيكل المشروع

```
src/
├── app/                    # صفحات Next.js App Router
│   ├── auth/              # صفحات المصادقة
│   ├── dashboard/         # صفحات لوحة التحكم
│   └── globals.css        # الأنماط العامة
├── components/            # مكونات React
│   ├── auth/             # مكونات المصادقة
│   ├── dashboard/        # مكونات لوحة التحكم
│   ├── layout/           # مكونات التخطيط
│   └── ui/               # مكونات واجهة المستخدم
├── lib/                  # مكتبات ووظائف مساعدة
├── types/                # تعريفات TypeScript
└── utils/                # وظائف مساعدة
```

## المميزات التقنية

### 🔒 الأمان
- Row Level Security (RLS) في Supabase
- مصادقة آمنة مع JWT
- حماية الطرق والصفحات
- تشفير البيانات الحساسة

### 🌐 دعم RTL
- دعم كامل للغة العربية
- تخطيط RTL مع Tailwind CSS
- خطوط عربية محسنة
- واجهة مستخدم باللغة العربية

### 📱 التصميم المتجاوب
- يعمل على جميع أحجام الشاشات
- تصميم mobile-first
- واجهة مستخدم حديثة ونظيفة

### ⚡ الأداء
- تحميل سريع مع Next.js
- تحسين الصور والموارد
- Lazy loading للمكونات
- تخزين مؤقت ذكي

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة، يرجى فتح issue في GitHub أو التواصل معنا.

---

تم تطوير هذا النظام بعناية لتلبية احتياجات إدارة العقارات والإيجارات في السوق العربي.
