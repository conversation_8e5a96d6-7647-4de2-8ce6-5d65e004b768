{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/BuildError.tsx"], "names": ["React", "Dialog", "DialogBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "Overlay", "Terminal", "VersionStalenessInfo", "noop", "css", "BuildError", "message", "versionInfo", "useCallback", "fixed", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "className", "h4", "id", "content", "footer", "p", "small", "styles"], "mappings": ";;;;;;;;;;AAAA,YAAYA,WAAW,QAAO;AAE9B,SACEC,MAAM,EACNC,UAAU,EACVC,aAAa,EACbC,YAAY,QACP,uBAAsB;AAC7B,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;AACjD,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,QAAQC,GAAG,QAAQ,2BAA0B;AAItD,OAAO,MAAMC,aAAwC,SAASA,WAAW,KAGxE;IAHwE,IAAA,EACvEC,OAAO,EACPC,WAAW,EACZ,GAHwE;IAIvE,MAAMJ,OAAOR,MAAMa,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,oBAACR;QAAQS,OAAAA;qBACP,oBAACb;QACCc,MAAK;QACLC,mBAAgB;QAChBC,oBAAiB;QACjBC,SAASV;qBAET,oBAACL,mCACC,oBAACC;QAAae,WAAU;qBACtB,oBAACC;QAAGC,IAAG;OAAsC,sBAC5CT,4BAAc,oBAACL,sBAAyBK,eAAkB,qBAE7D,oBAACV;QAAWiB,WAAU;qBACpB,oBAACb;QAASgB,SAASX;sBACnB,oBAACY,8BACC,oBAACC;QAAEH,IAAG;qBACJ,oBAACI,eAAM;AAWvB,EAAC;AAED,OAAO,MAAMC,SAASjB,uBAqBrB"}