-- سياسات الأمان (Row Level Security) لنظام إدارة الإيجارات
-- يجب تشغيل هذا الملف بعد supabase-setup.sql

-- تفعيل RLS على جميع الجداول
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE leases ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- سياسات جدول الملفات الشخصية (profiles)
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات جدول العقارات (properties)
CREATE POLICY "Users can view own properties" ON properties
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own properties" ON properties
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own properties" ON properties
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own properties" ON properties
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول المستأجرين (tenants)
CREATE POLICY "Users can view own tenants" ON tenants
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tenants" ON tenants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tenants" ON tenants
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tenants" ON tenants
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول عقود الإيجار (leases)
CREATE POLICY "Users can view own leases" ON leases
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own leases" ON leases
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own leases" ON leases
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own leases" ON leases
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول المدفوعات (payments)
CREATE POLICY "Users can view own payments" ON payments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payments" ON payments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own payments" ON payments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own payments" ON payments
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول المصروفات (expenses)
CREATE POLICY "Users can view own expenses" ON expenses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own expenses" ON expenses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own expenses" ON expenses
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own expenses" ON expenses
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات جدول الفواتير (invoices)
CREATE POLICY "Users can view own invoices" ON invoices
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own invoices" ON invoices
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own invoices" ON invoices
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own invoices" ON invoices
    FOR DELETE USING (auth.uid() = user_id);

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء trigger لإنشاء ملف شخصي تلقائياً
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- دالة لتحديث حالة المدفوعات المتأخرة
CREATE OR REPLACE FUNCTION update_overdue_payments()
RETURNS void AS $$
BEGIN
    UPDATE payments 
    SET status = 'overdue'
    WHERE status = 'pending' 
    AND due_date < CURRENT_DATE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة لتحديث حالة العقود المنتهية
CREATE OR REPLACE FUNCTION update_expired_leases()
RETURNS void AS $$
BEGIN
    UPDATE leases 
    SET status = 'expired'
    WHERE status = 'active' 
    AND end_date < CURRENT_DATE;
    
    -- تحديث حالة العقارات المرتبطة بالعقود المنتهية
    UPDATE properties 
    SET status = 'available'
    WHERE id IN (
        SELECT property_id 
        FROM leases 
        WHERE status = 'expired' 
        AND end_date < CURRENT_DATE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة للحصول على إحصائيات المستخدم
CREATE OR REPLACE FUNCTION get_user_stats(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_properties', (SELECT COUNT(*) FROM properties WHERE user_id = user_uuid),
        'rented_properties', (SELECT COUNT(*) FROM properties WHERE user_id = user_uuid AND status = 'rented'),
        'available_properties', (SELECT COUNT(*) FROM properties WHERE user_id = user_uuid AND status = 'available'),
        'total_tenants', (SELECT COUNT(*) FROM tenants WHERE user_id = user_uuid),
        'active_leases', (SELECT COUNT(*) FROM leases WHERE user_id = user_uuid AND status = 'active'),
        'pending_payments', (SELECT COUNT(*) FROM payments WHERE user_id = user_uuid AND status = 'pending'),
        'overdue_payments', (SELECT COUNT(*) FROM payments WHERE user_id = user_uuid AND status = 'overdue'),
        'monthly_income', (
            SELECT COALESCE(SUM(amount), 0) 
            FROM payments 
            WHERE user_id = user_uuid 
            AND status = 'paid' 
            AND EXTRACT(MONTH FROM payment_date) = EXTRACT(MONTH FROM CURRENT_DATE)
            AND EXTRACT(YEAR FROM payment_date) = EXTRACT(YEAR FROM CURRENT_DATE)
        ),
        'monthly_expenses', (
            SELECT COALESCE(SUM(amount), 0) 
            FROM expenses 
            WHERE user_id = user_uuid 
            AND EXTRACT(MONTH FROM expense_date) = EXTRACT(MONTH FROM CURRENT_DATE)
            AND EXTRACT(YEAR FROM expense_date) = EXTRACT(YEAR FROM CURRENT_DATE)
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
