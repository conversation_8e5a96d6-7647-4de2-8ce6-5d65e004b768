'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getCurrentUser } from '@/lib/supabase'
import LoginForm from '@/components/auth/LoginForm'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function HomePage() {
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState(null)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (currentUser) {
        router.push('/dashboard')
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('خطأ في التحقق من المستخدم:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100">
      <div className="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-primary-900 mb-2">
              نظام إدارة الإيجارات
            </h1>
            <p className="text-lg text-gray-600">
              نظام شامل لإدارة العقارات والمحاسبة المكتبية
            </p>
          </div>
          
          <div className="bg-white rounded-lg shadow-xl p-8">
            <LoginForm onSuccess={() => router.push('/dashboard')} />
          </div>
          
          <div className="text-center">
            <p className="text-sm text-gray-500">
              لا تملك حساب؟{' '}
              <button 
                onClick={() => router.push('/auth/register')}
                className="text-primary-600 hover:text-primary-500 font-medium"
              >
                إنشاء حساب جديد
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
