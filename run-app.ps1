Write-Host "🏢 نظام إدارة الإيجارات والمحاسبة المكتبية" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود Node.js
Write-Host "[1/3] التحقق من Node.js..." -ForegroundColor Yellow
$nodePath = "C:\Program Files\nodejs\node.exe"
if (Test-Path $nodePath) {
    Write-Host "✅ Node.js مثبت" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js غير مثبت" -ForegroundColor Red
    Write-Host "يرجى تثبيت Node.js من https://nodejs.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "[2/3] تثبيت التبعيات..." -ForegroundColor Yellow

try {
    & "C:\Program Files\nodejs\npm.cmd" install 2>$null
    Write-Host "✅ تم تثبيت التبعيات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ تحذير: قد تكون هناك مشاكل في التبعيات" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "[3/3] تشغيل الخادم..." -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 افتح المتصفح واذهب إلى: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔄 لإيقاف الخادم اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host ""

try {
    & "C:\Program Files\nodejs\npm.cmd" run dev
} catch {
    Write-Host "❌ خطأ في تشغيل الخادم" -ForegroundColor Red
    Write-Host "تحقق من تثبيت Node.js والتبعيات" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "تم إيقاف الخادم" -ForegroundColor Yellow
Read-Host "اضغط Enter للخروج"
