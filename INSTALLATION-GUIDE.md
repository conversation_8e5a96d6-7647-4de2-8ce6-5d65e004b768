# دليل التثبيت - نظام إدارة الإيجارات

## 🚀 طرق التثبيت والتشغيل

### الطريقة الأولى: استخدام ملف PowerShell (الأسهل)

1. **افتح PowerShell كمدير**:
   - اضغط `Win + X`
   - اختر "Windows PowerShell (Admin)" أو "Terminal (Admin)"

2. **انتقل إلى مجلد المشروع**:
   ```powershell
   cd "C:\Users\<USER>\OneDrive\مواقع\AS2030"
   ```

3. **تشغيل سكريبت التثبيت**:
   ```powershell
   .\install-and-run.ps1
   ```

   إذا ظهرت رسالة خطأ حول سياسة التنفيذ، قم بتشغيل:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### الطريقة الثانية: استخدام ملف Batch

1. **انقر نقراً مزدوجاً على ملف**:
   ```
   install-and-run.bat
   ```

### الطريقة الثالثة: التثبيت اليدوي

#### 1. تثبيت Node.js

**الخيار أ: تحميل من الموقع الرسمي**
- اذهب إلى: https://nodejs.org
- حمل النسخة LTS (الموصى بها)
- قم بتثبيتها واتبع التعليمات

**الخيار ب: استخدام winget**
```powershell
winget install OpenJS.NodeJS
```

**الخيار ج: استخدام Chocolatey**
```powershell
choco install nodejs
```

#### 2. إعادة تشغيل PowerShell
بعد تثبيت Node.js، أغلق وأعد فتح PowerShell

#### 3. التحقق من التثبيت
```powershell
node --version
npm --version
```

#### 4. تثبيت تبعيات المشروع
```powershell
cd "C:\Users\<USER>\OneDrive\مواقع\AS2030"
npm install
```

#### 5. إعداد ملف البيئة
```powershell
copy .env.local.example .env.local
```

#### 6. تشغيل التطبيق
```powershell
npm run dev
```

## 🔧 إعداد Supabase

### 1. إنشاء مشروع Supabase
1. اذهب إلى: https://supabase.com
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ مشروع جديد

### 2. الحصول على بيانات الاتصال
1. في لوحة تحكم Supabase، اذهب إلى Settings > API
2. انسخ:
   - Project URL
   - anon public key
   - service_role key (اختياري)

### 3. تحديث ملف البيئة
افتح ملف `.env.local` وأضف:
```env
NEXT_PUBLIC_SUPABASE_URL=your_project_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### 4. إعداد قاعدة البيانات
1. في Supabase، اذهب إلى SQL Editor
2. انسخ محتوى ملف `supabase-setup.sql` وشغله
3. انسخ محتوى ملف `supabase-rls-policies.sql` وشغله

## 🌐 الوصول للتطبيق

بعد تشغيل `npm run dev`، افتح المتصفح واذهب إلى:
```
http://localhost:3000
```

## 🔍 حل المشاكل الشائعة

### مشكلة: "node is not recognized"
**الحل**: 
1. تأكد من تثبيت Node.js
2. أعد تشغيل PowerShell
3. أضف Node.js إلى PATH يدوياً:
   ```powershell
   $env:PATH += ";C:\Program Files\nodejs"
   ```

### مشكلة: "npm install fails"
**الحل**:
1. احذف مجلد `node_modules` إن وجد
2. احذف ملف `package-lock.json` إن وجد
3. شغل `npm install` مرة أخرى

### مشكلة: "Permission denied"
**الحل**:
1. شغل PowerShell كمدير
2. أو غير سياسة التنفيذ:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

### مشكلة: "Supabase connection error"
**الحل**:
1. تأكد من صحة بيانات `.env.local`
2. تأكد من تشغيل ملفات SQL في Supabase
3. تأكد من تفعيل RLS policies

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تأكد من اتباع الخطوات بالترتيب
2. تحقق من رسائل الخطأ في Terminal
3. تأكد من اتصال الإنترنت
4. راجع ملف README.md للمزيد من التفاصيل

---

**ملاحظة**: هذا التطبيق يتطلب Node.js 18+ للعمل بشكل صحيح.
