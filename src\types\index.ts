// أنواع البيانات الأساسية للتطبيق

export interface User {
  id: string
  email: string
  full_name: string
  company_name?: string
  phone?: string
  created_at: string
  updated_at: string
}

export interface Property {
  id: string
  user_id: string
  name: string
  address: string
  property_type: 'apartment' | 'house' | 'commercial' | 'office'
  monthly_rent: number
  deposit_amount: number
  description?: string
  status: 'available' | 'rented' | 'maintenance'
  created_at: string
  updated_at: string
}

export interface Tenant {
  id: string
  user_id: string
  full_name: string
  phone: string
  email?: string
  national_id: string
  emergency_contact?: string
  created_at: string
  updated_at: string
}

export interface Lease {
  id: string
  user_id: string
  property_id: string
  tenant_id: string
  start_date: string
  end_date: string
  monthly_rent: number
  deposit_paid: number
  status: 'active' | 'expired' | 'terminated'
  created_at: string
  updated_at: string
  // العلاقات
  property?: Property
  tenant?: Tenant
}

export interface Payment {
  id: string
  user_id: string
  lease_id: string
  amount: number
  payment_date: string
  due_date: string
  payment_method: 'cash' | 'bank_transfer' | 'check' | 'card'
  status: 'paid' | 'pending' | 'overdue'
  notes?: string
  created_at: string
  updated_at: string
  // العلاقات
  lease?: Lease
}

export interface Expense {
  id: string
  user_id: string
  property_id?: string
  category: 'maintenance' | 'utilities' | 'insurance' | 'taxes' | 'management' | 'other'
  amount: number
  description: string
  expense_date: string
  receipt_url?: string
  created_at: string
  updated_at: string
  // العلاقات
  property?: Property
}

export interface Invoice {
  id: string
  user_id: string
  lease_id: string
  invoice_number: string
  amount: number
  due_date: string
  status: 'draft' | 'sent' | 'paid' | 'overdue'
  created_at: string
  updated_at: string
  // العلاقات
  lease?: Lease
}

// أنواع للإحصائيات والتقارير
export interface DashboardStats {
  total_properties: number
  rented_properties: number
  available_properties: number
  total_tenants: number
  monthly_income: number
  pending_payments: number
  overdue_payments: number
  total_expenses: number
}

export interface MonthlyReport {
  month: string
  income: number
  expenses: number
  profit: number
  payments_count: number
  overdue_count: number
}

// أنواع للنماذج
export interface PropertyFormData {
  name: string
  address: string
  property_type: 'apartment' | 'house' | 'commercial' | 'office'
  monthly_rent: number
  deposit_amount: number
  description?: string
  status: 'available' | 'rented' | 'maintenance'
}

export interface TenantFormData {
  full_name: string
  phone: string
  email?: string
  national_id: string
  emergency_contact?: string
}

export interface LeaseFormData {
  property_id: string
  tenant_id: string
  start_date: string
  end_date: string
  monthly_rent: number
  deposit_paid: number
}

export interface PaymentFormData {
  lease_id: string
  amount: number
  payment_date: string
  due_date: string
  payment_method: 'cash' | 'bank_transfer' | 'check' | 'card'
  status: 'paid' | 'pending' | 'overdue'
  notes?: string
}

export interface ExpenseFormData {
  property_id?: string
  category: 'maintenance' | 'utilities' | 'insurance' | 'taxes' | 'management' | 'other'
  amount: number
  description: string
  expense_date: string
  receipt_url?: string
}

// أنواع للمكونات
export interface TableColumn {
  key: string
  label: string
  sortable?: boolean
  render?: (value: any, row: any) => React.ReactNode
}

export interface FilterOption {
  label: string
  value: string
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
}
