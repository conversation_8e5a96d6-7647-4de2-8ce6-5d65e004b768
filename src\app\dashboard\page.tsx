'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { DashboardStats } from '@/types'
import StatsCards from '@/components/dashboard/StatsCards'
import RecentPayments from '@/components/dashboard/RecentPayments'
import OverduePayments from '@/components/dashboard/OverduePayments'
import MonthlyChart from '@/components/dashboard/MonthlyChart'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      // جلب إحصائيات العقارات
      const { data: properties } = await supabase
        .from('properties')
        .select('*')
        .eq('user_id', user.id)

      // جلب إحصائيات المستأجرين
      const { data: tenants } = await supabase
        .from('tenants')
        .select('*')
        .eq('user_id', user.id)

      // جلب إحصائيات المدفوعات
      const { data: payments } = await supabase
        .from('payments')
        .select('*')
        .eq('user_id', user.id)

      // جلب إحصائيات المصروفات
      const { data: expenses } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id)

      // حساب الإحصائيات
      const totalProperties = properties?.length || 0
      const rentedProperties = properties?.filter(p => p.status === 'rented').length || 0
      const availableProperties = properties?.filter(p => p.status === 'available').length || 0
      const totalTenants = tenants?.length || 0

      // حساب الدخل الشهري
      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()
      const monthlyIncome = payments
        ?.filter(p => {
          const paymentDate = new Date(p.payment_date)
          return paymentDate.getMonth() === currentMonth && 
                 paymentDate.getFullYear() === currentYear &&
                 p.status === 'paid'
        })
        .reduce((sum, p) => sum + p.amount, 0) || 0

      // حساب المدفوعات المعلقة والمتأخرة
      const pendingPayments = payments?.filter(p => p.status === 'pending').length || 0
      const overduePayments = payments?.filter(p => p.status === 'overdue').length || 0

      // حساب إجمالي المصروفات الشهرية
      const totalExpenses = expenses
        ?.filter(e => {
          const expenseDate = new Date(e.expense_date)
          return expenseDate.getMonth() === currentMonth && 
                 expenseDate.getFullYear() === currentYear
        })
        .reduce((sum, e) => sum + e.amount, 0) || 0

      setStats({
        total_properties: totalProperties,
        rented_properties: rentedProperties,
        available_properties: availableProperties,
        total_tenants: totalTenants,
        monthly_income: monthlyIncome,
        pending_payments: pendingPayments,
        overdue_payments: overduePayments,
        total_expenses: totalExpenses,
      })
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">لوحة التحكم</h1>
        <p className="mt-1 text-sm text-gray-500">
          نظرة عامة على أداء إدارة العقارات والإيجارات
        </p>
      </div>

      {/* Stats cards */}
      {stats && <StatsCards stats={stats} />}

      {/* Charts and recent activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="lg:col-span-2">
          <MonthlyChart />
        </div>
      </div>

      {/* Recent payments and overdue */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RecentPayments />
        <OverduePayments />
      </div>
    </div>
  )
}
