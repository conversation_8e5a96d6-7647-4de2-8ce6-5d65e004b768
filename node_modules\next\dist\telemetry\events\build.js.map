{"version": 3, "sources": ["../../../src/telemetry/events/build.ts"], "names": ["eventTypeCheckCompleted", "eventLintCheckCompleted", "eventBuildCompleted", "eventBuildOptimize", "EVENT_BUILD_FEATURE_USAGE", "eventBuildFeatureUsage", "EVENT_NAME_PACKAGE_USED_IN_GET_SERVER_SIDE_PROPS", "eventPackageUsedInGetServerSideProps", "REGEXP_DIRECTORY_DUNDER", "REGEXP_DIRECTORY_TESTS", "REGEXP_FILE_TEST", "EVENT_TYPE_CHECK_COMPLETED", "event", "eventName", "payload", "EVENT_LINT_CHECK_COMPLETED", "EVENT_BUILD_COMPLETED", "pagePaths", "totalPageCount", "length", "hasDunderPages", "some", "path", "test", "hasTestPages", "totalAppPagesCount", "EVENT_BUILD_OPTIMIZED", "staticAppPagesCount", "serverAppPagesCount", "edgeRuntimeAppCount", "edgeRuntimePagesCount", "telemetryPlugin", "usages", "map", "featureName", "invocationCount", "packagesUsedInServerSideProps", "packageName", "package"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAiBgBA,uBAAuB;eAAvBA;;IAyBAC,uBAAuB;eAAvBA;;IAmBAC,mBAAmB;eAAnBA;;IAkDAC,kBAAkB;eAAlBA;;IA4BHC,yBAAyB;eAAzBA;;IAqCGC,sBAAsB;eAAtBA;;IAYHC,gDAAgD;eAAhDA;;IAOGC,oCAAoC;eAApCA;;;AAhMhB,MAAMC,0BACJ;AACF,MAAMC,yBAAyB;AAC/B,MAAMC,mBAAmB;AAEzB,MAAMC,6BAA6B;AAS5B,SAASX,wBAAwBY,KAA8B;IAIpE,OAAO;QACLC,WAAWF;QACXG,SAASF;IACX;AACF;AAEA,MAAMG,6BAA6B;AAe5B,SAASd,wBAAwBW,KAA8B;IAIpE,OAAO;QACLC,WAAWE;QACXD,SAASF;IACX;AACF;AAEA,MAAMI,wBAAwB;AASvB,SAASd,oBACde,SAAmB,EACnBL,KAGC;IAED,OAAO;QACLC,WAAWG;QACXF,SAAS;YACP,GAAGF,KAAK;YACRM,gBAAgBD,UAAUE,MAAM;YAChCC,gBAAgBH,UAAUI,IAAI,CAAC,CAACC,OAC9Bd,wBAAwBe,IAAI,CAACD;YAE/BE,cAAcP,UAAUI,IAAI,CAC1B,CAACC,OACCb,uBAAuBc,IAAI,CAACD,SAASZ,iBAAiBa,IAAI,CAACD;YAE/DG,oBAAoBb,MAAMa,kBAAkB;QAC9C;IACF;AACF;AAEA,MAAMC,wBAAwB;AA0BvB,SAASvB,mBACdc,SAAmB,EACnBL,KAGC;IAED,OAAO;QACLC,WAAWa;QACXZ,SAAS;YACP,GAAGF,KAAK;YACRM,gBAAgBD,UAAUE,MAAM;YAChCC,gBAAgBH,UAAUI,IAAI,CAAC,CAACC,OAC9Bd,wBAAwBe,IAAI,CAACD;YAE/BE,cAAcP,UAAUI,IAAI,CAC1B,CAACC,OACCb,uBAAuBc,IAAI,CAACD,SAASZ,iBAAiBa,IAAI,CAACD;YAE/DG,oBAAoBb,MAAMa,kBAAkB;YAC5CE,qBAAqBf,MAAMe,mBAAmB;YAC9CC,qBAAqBhB,MAAMgB,mBAAmB;YAC9CC,qBAAqBjB,MAAMiB,mBAAmB;YAC9CC,uBAAuBlB,MAAMkB,qBAAqB;QACpD;IACF;AACF;AAEO,MAAM1B,4BAA4B;AAqClC,SAASC,uBACd0B,eAAgC;IAEhC,OAAOA,gBAAgBC,MAAM,GAAGC,GAAG,CAAC,CAAC,EAAEC,WAAW,EAAEC,eAAe,EAAE,GAAM,CAAA;YACzEtB,WAAWT;YACXU,SAAS;gBACPoB;gBACAC;YACF;QACF,CAAA;AACF;AAEO,MAAM7B,mDACX;AAMK,SAASC,qCACdwB,eAAgC;IAEhC,OAAOA,gBAAgBK,6BAA6B,GAAGH,GAAG,CAAC,CAACI,cAAiB,CAAA;YAC3ExB,WAAWP;YACXQ,SAAS;gBACPwB,SAASD;YACX;QACF,CAAA;AACF"}