@echo off
echo Starting Rental Management System...
echo.

REM Add Node.js to PATH
set "PATH=%PATH%;C:\Program Files\nodejs"

REM Check if Node.js is installed
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo Node.js not found. Please install Node.js from https://nodejs.org
    echo Or run: winget install OpenJS.NodeJS
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
call npm install

REM Create environment file if it doesn't exist
if not exist .env.local (
    copy .env.local.example .env.local
    echo Created .env.local file. Please update it with your Supabase credentials.
)

REM Start the development server
echo Starting development server...
echo Open http://localhost:3000 in your browser
call npm run dev

pause
