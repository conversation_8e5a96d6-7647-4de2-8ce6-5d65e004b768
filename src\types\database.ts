export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string
          company_name: string | null
          phone: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          company_name?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          company_name?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      properties: {
        Row: {
          id: string
          user_id: string
          name: string
          address: string
          property_type: 'apartment' | 'house' | 'commercial' | 'office'
          monthly_rent: number
          deposit_amount: number
          description: string | null
          status: 'available' | 'rented' | 'maintenance'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          address: string
          property_type: 'apartment' | 'house' | 'commercial' | 'office'
          monthly_rent: number
          deposit_amount: number
          description?: string | null
          status?: 'available' | 'rented' | 'maintenance'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          address?: string
          property_type?: 'apartment' | 'house' | 'commercial' | 'office'
          monthly_rent?: number
          deposit_amount?: number
          description?: string | null
          status?: 'available' | 'rented' | 'maintenance'
          created_at?: string
          updated_at?: string
        }
      }
      tenants: {
        Row: {
          id: string
          user_id: string
          full_name: string
          phone: string
          email: string | null
          national_id: string
          emergency_contact: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          full_name: string
          phone: string
          email?: string | null
          national_id: string
          emergency_contact?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          full_name?: string
          phone?: string
          email?: string | null
          national_id?: string
          emergency_contact?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      leases: {
        Row: {
          id: string
          user_id: string
          property_id: string
          tenant_id: string
          start_date: string
          end_date: string
          monthly_rent: number
          deposit_paid: number
          status: 'active' | 'expired' | 'terminated'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          property_id: string
          tenant_id: string
          start_date: string
          end_date: string
          monthly_rent: number
          deposit_paid: number
          status?: 'active' | 'expired' | 'terminated'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          property_id?: string
          tenant_id?: string
          start_date?: string
          end_date?: string
          monthly_rent?: number
          deposit_paid?: number
          status?: 'active' | 'expired' | 'terminated'
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          user_id: string
          lease_id: string
          amount: number
          payment_date: string
          due_date: string
          payment_method: 'cash' | 'bank_transfer' | 'check' | 'card'
          status: 'paid' | 'pending' | 'overdue'
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          lease_id: string
          amount: number
          payment_date?: string
          due_date: string
          payment_method: 'cash' | 'bank_transfer' | 'check' | 'card'
          status?: 'paid' | 'pending' | 'overdue'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          lease_id?: string
          amount?: number
          payment_date?: string
          due_date?: string
          payment_method?: 'cash' | 'bank_transfer' | 'check' | 'card'
          status?: 'paid' | 'pending' | 'overdue'
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          user_id: string
          property_id: string | null
          category: 'maintenance' | 'utilities' | 'insurance' | 'taxes' | 'management' | 'other'
          amount: number
          description: string
          expense_date: string
          receipt_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          property_id?: string | null
          category: 'maintenance' | 'utilities' | 'insurance' | 'taxes' | 'management' | 'other'
          amount: number
          description: string
          expense_date: string
          receipt_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          property_id?: string | null
          category?: 'maintenance' | 'utilities' | 'insurance' | 'taxes' | 'management' | 'other'
          amount?: number
          description?: string
          expense_date?: string
          receipt_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      invoices: {
        Row: {
          id: string
          user_id: string
          lease_id: string
          invoice_number: string
          amount: number
          due_date: string
          status: 'draft' | 'sent' | 'paid' | 'overdue'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          lease_id: string
          invoice_number: string
          amount: number
          due_date: string
          status?: 'draft' | 'sent' | 'paid' | 'overdue'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          lease_id?: string
          invoice_number?: string
          amount?: number
          due_date?: string
          status?: 'draft' | 'sent' | 'paid' | 'overdue'
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
