'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase'
import { Payment } from '@/types'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'
import { CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

export default function RecentPayments() {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadRecentPayments()
  }, [])

  const loadRecentPayments = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { data, error } = await supabase
        .from('payments')
        .select(`
          *,
          lease:leases (
            property:properties (name),
            tenant:tenants (full_name)
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5)

      if (error) throw error
      setPayments(data || [])
    } catch (error) {
      console.error('خطأ في جلب المدفوعات الحديثة:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircleIcon className="h-5 w-5 text-success-500" />
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-warning-500" />
      case 'overdue':
        return <ClockIcon className="h-5 w-5 text-danger-500" />
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'مدفوع'
      case 'pending':
        return 'معلق'
      case 'overdue':
        return 'متأخر'
      default:
        return status
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-success-100 text-success-800'
      case 'pending':
        return 'bg-warning-100 text-warning-800'
      case 'overdue':
        return 'bg-danger-100 text-danger-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">المدفوعات الحديثة</h3>
        </div>
        <div className="flex items-center justify-center h-32">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">المدفوعات الحديثة</h3>
        <Link
          href="/dashboard/payments"
          className="text-sm text-primary-600 hover:text-primary-500"
        >
          عرض الكل
        </Link>
      </div>

      {payments.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">لا توجد مدفوعات حديثة</p>
        </div>
      ) : (
        <div className="space-y-3">
          {payments.map((payment) => (
            <div
              key={payment.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div className="flex items-center space-x-3 space-x-reverse">
                {getStatusIcon(payment.status)}
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {payment.lease?.tenant?.full_name || 'غير محدد'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {payment.lease?.property?.name || 'غير محدد'}
                  </p>
                  <p className="text-xs text-gray-400">
                    {format(new Date(payment.due_date), 'dd MMMM yyyy', { locale: ar })}
                  </p>
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-gray-900">
                  {payment.amount.toLocaleString()} ر.س
                </p>
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                    payment.status
                  )}`}
                >
                  {getStatusText(payment.status)}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
