{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts"], "names": ["applyRouterStatePatchToTree", "applyPatch", "initialTree", "patchTree", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "matchSegment", "newParallelRoutes", "key", "isInPatchTreeParallelRoutes", "tree", "flightSegmentPath", "flightRouterState", "treePatch", "segment", "parallelRoutes", "isRootLayout", "length", "currentSegment", "parallelRouteKey", "lastSegment", "parallelRoutePatch", "slice"], "mappings": ";;;;+BAoEgBA;;;eAAAA;;;+BAhEa;AAE7B;;CAEC,GACD,SAASC,WACPC,WAA8B,EAC9BC,SAA4B;IAE5B,MAAM,CAACC,gBAAgBC,sBAAsB,GAAGH;IAChD,MAAM,CAACI,cAAcC,oBAAoB,GAAGJ;IAE5C,gGAAgG;IAChG,iFAAiF;IACjF,IAAIG,iBAAiB,iBAAiBF,mBAAmB,eAAe;QACtE,OAAOF;IACT;IAEA,IAAIM,IAAAA,2BAAY,EAACJ,gBAAgBE,eAAe;QAC9C,MAAMG,oBAA0C,CAAC;QACjD,IAAK,MAAMC,OAAOL,sBAAuB;YACvC,MAAMM,8BACJ,OAAOJ,mBAAmB,CAACG,IAAI,KAAK;YACtC,IAAIC,6BAA6B;gBAC/BF,iBAAiB,CAACC,IAAI,GAAGT,WACvBI,qBAAqB,CAACK,IAAI,EAC1BH,mBAAmB,CAACG,IAAI;YAE5B,OAAO;gBACLD,iBAAiB,CAACC,IAAI,GAAGL,qBAAqB,CAACK,IAAI;YACrD;QACF;QAEA,IAAK,MAAMA,OAAOH,oBAAqB;YACrC,IAAIE,iBAAiB,CAACC,IAAI,EAAE;gBAC1B;YACF;YAEAD,iBAAiB,CAACC,IAAI,GAAGH,mBAAmB,CAACG,IAAI;QACnD;QAEA,MAAME,OAA0B;YAACR;YAAgBK;SAAkB;QAEnE,IAAIP,WAAW,CAAC,EAAE,EAAE;YAClBU,IAAI,CAAC,EAAE,GAAGV,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBU,IAAI,CAAC,EAAE,GAAGV,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBU,IAAI,CAAC,EAAE,GAAGV,WAAW,CAAC,EAAE;QAC1B;QAEA,OAAOU;IACT;IAEA,OAAOT;AACT;AAKO,SAASH,4BACda,iBAAoC,EACpCC,iBAAoC,EACpCC,SAA4B;IAE5B,MAAM,CAACC,SAASC,oBAAoBC,aAAa,GAAGJ;IAEpD,eAAe;IACf,IAAID,kBAAkBM,MAAM,KAAK,GAAG;QAClC,MAAMP,OAA0BX,WAAWa,mBAAmBC;QAE9D,OAAOH;IACT;IAEA,MAAM,CAACQ,gBAAgBC,iBAAiB,GAAGR;IAE3C,iGAAiG;IACjG,IAAI,CAACL,IAAAA,2BAAY,EAACY,gBAAgBJ,UAAU;QAC1C,OAAO;IACT;IAEA,MAAMM,cAAcT,kBAAkBM,MAAM,KAAK;IAEjD,IAAII;IACJ,IAAID,aAAa;QACfC,qBAAqBtB,WAAWgB,cAAc,CAACI,iBAAiB,EAAEN;IACpE,OAAO;QACLQ,qBAAqBvB,4BACnBa,kBAAkBW,KAAK,CAAC,IACxBP,cAAc,CAACI,iBAAiB,EAChCN;QAGF,IAAIQ,uBAAuB,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,MAAMX,OAA0B;QAC9BC,iBAAiB,CAAC,EAAE;QACpB;YACE,GAAGI,cAAc;YACjB,CAACI,iBAAiB,EAAEE;QACtB;KACD;IAED,qCAAqC;IACrC,IAAIL,cAAc;QAChBN,IAAI,CAAC,EAAE,GAAG;IACZ;IAEA,OAAOA;AACT"}