'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { signIn } from '@/lib/supabase'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

interface LoginFormData {
  email: string
  password: string
}

interface LoginFormProps {
  onSuccess: () => void
}

export default function LoginForm({ onSuccess }: LoginFormProps) {
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>()

  const onSubmit = async (data: LoginFormData) => {
    setLoading(true)
    setError('')

    try {
      const { data: authData, error: authError } = await signIn(data.email, data.password)
      
      if (authError) {
        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة')
        return
      }

      if (authData.user) {
        onSuccess()
      }
    } catch (err) {
      setError('حدث خطأ أثناء تسجيل الدخول')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          البريد الإلكتروني
        </label>
        <input
          {...register('email', {
            required: 'البريد الإلكتروني مطلوب',
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: 'البريد الإلكتروني غير صحيح'
            }
          })}
          type="email"
          id="email"
          className="input-field"
          placeholder="أدخل بريدك الإلكتروني"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-danger-600">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
          كلمة المرور
        </label>
        <div className="relative">
          <input
            {...register('password', {
              required: 'كلمة المرور مطلوبة',
              minLength: {
                value: 6,
                message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
              }
            })}
            type={showPassword ? 'text' : 'password'}
            id="password"
            className="input-field pl-10"
            placeholder="أدخل كلمة المرور"
          />
          <button
            type="button"
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeSlashIcon className="h-5 w-5" />
            ) : (
              <EyeIcon className="h-5 w-5" />
            )}
          </button>
        </div>
        {errors.password && (
          <p className="mt-1 text-sm text-danger-600">{errors.password.message}</p>
        )}
      </div>

      {error && (
        <div className="bg-danger-50 border border-danger-200 rounded-md p-3">
          <p className="text-sm text-danger-600">{error}</p>
        </div>
      )}

      <button
        type="submit"
        disabled={loading}
        className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
      </button>
    </form>
  )
}
