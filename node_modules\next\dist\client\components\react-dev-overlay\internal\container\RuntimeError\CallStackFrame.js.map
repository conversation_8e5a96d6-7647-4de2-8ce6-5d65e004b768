{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.tsx"], "names": ["CallStackFrame", "frame", "f", "originalStackFrame", "sourceStackFrame", "hasSource", "Boolean", "originalCodeFrame", "open", "useOpenInEditor", "file", "lineNumber", "column", "undefined", "div", "data-nextjs-call-stack-frame", "h3", "data-nextjs-frame-expanded", "expanded", "methodName", "data-has-source", "tabIndex", "role", "onClick", "title", "span", "getFrameSource", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;gEARK;4BAKX;iCACyB;AAEzB,MAAMA,iBAER,SAASA,eAAe,KAAS;IAAT,IAAA,EAAEC,KAAK,EAAE,GAAT;QAILA;IAHtB,0CAA0C;IAC1C,2CAA2C;IAE3C,MAAMC,IAAgBD,CAAAA,4BAAAA,MAAME,kBAAkB,YAAxBF,4BAA4BA,MAAMG,gBAAgB;IACxE,MAAMC,YAAYC,QAAQL,MAAMM,iBAAiB;IACjD,MAAMC,OAAOC,IAAAA,gCAAe,EAC1BJ,YACI;QACEK,MAAMR,EAAEQ,IAAI;QACZC,YAAYT,EAAES,UAAU;QACxBC,QAAQV,EAAEU,MAAM;IAClB,IACAC;IAGN,qBACE,6BAACC;QAAIC,gCAAAA;qBACH,6BAACC;QAAGC,8BAA4BX,QAAQL,MAAMiB,QAAQ;OACnDhB,EAAEiB,UAAU,iBAEf,6BAACL;QACCM,mBAAiBf,YAAY,SAASQ;QACtCQ,UAAUhB,YAAY,KAAKQ;QAC3BS,MAAMjB,YAAY,SAASQ;QAC3BU,SAASf;QACTgB,OAAOnB,YAAY,iCAAiCQ;qBAEpD,6BAACY,cAAMC,IAAAA,0BAAc,EAACxB,mBACtB,6BAACyB;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,6BAACC;QAAKC,GAAE;sBACR,6BAACC;QAASC,QAAO;sBACjB,6BAACC;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;;AAK3C"}